import { Tool } from "../..";
import { BUILT_IN_GROUP_NAME, BuiltInToolNames } from "../builtIn";
import { createSystemMessageExampleCall } from "../systemMessageTools/buildToolsSystemMessage";

const grepToolDescription = `- Fast content search tool that works with any codebase size
- Searches file contents using regular expressions
- Supports full regex syntax (e.g. "log.*Error", "function\\s+\\w+", etc.)
- Filter files by pattern with the include parameter (eg. "*.js", "*.{ts,tsx}")
- Returns matching file paths sorted by modification time
- Use this tool when you need to find files containing specific patterns
- When you are doing an open-ended search that may require multiple rounds of globbing and grepping, use the Agent tool instead`;

export const grepSearchTool: Tool = {
  type: "function",
  displayTitle: "查询关键词",
  wouldLikeTo: 'search for "{{{ pattern }}}" in the repository',
  isCurrently: 'getting search results for "{{{ pattern }}}"',
  hasAlready: 'retrieved search results for "{{{ pattern }}}"',
  readonly: true,
  isInstant: true,
  group: BUILT_IN_GROUP_NAME,
  function: {
    name: BuiltInToolNames.GrepSearch,
    description: grepToolDescription,
    parameters: {
      type: "object",
      required: ["query"],
      properties: {
        pattern: {
          type: "string",
          description:
            "The regular expression pattern to search for in file contents",
        },
        path: {
          type: "string",
          description:
            "The directory to search in. Defaults to the current working directory. now is not supported",
        },
        include: {
          type: "string",
          description:
            'File pattern to include in the search (e.g. "*.js", "*.{ts,tsx}"). now is not supported',
        },
      },
    },
  },
  systemMessageDescription: createSystemMessageExampleCall(
    BuiltInToolNames.GrepSearch,
    `To perform a grep search within the project, call the ${BuiltInToolNames.GrepSearch} tool with the pattern to match. For example:`,
    [["pattern", ".*main_services.*"]],
  ),
};
