import { ToolImpl } from ".";
import { ContextItem } from "../..";
import { formatGrepSearchResults } from "../../util/grepSearch";
import { getOptionalStringArg, getStringArg } from "../parseArgs";

const DEFAULT_GREP_SEARCH_RESULTS_LIMIT = 100;
const DEFAULT_GREP_SEARCH_CHAR_LIMIT = 5000; // ~1000 tokens, will keep truncation simply for now

function splitGrepResultsByFile(content: string): ContextItem[] {
  const matches = [...content.matchAll(/^\.\/([^\n]+)$/gm)];

  if (matches.length === 0) {
    return [];
  }

  const contextItems: ContextItem[] = [];

  for (let i = 0; i < matches.length; i++) {
    const match = matches[i];
    const filepath = match[1];
    const startIndex = match.index!;
    const endIndex =
      i < matches.length - 1 ? matches[i + 1].index! : content.length;

    // Extract grepped content for this file
    const fileContent = content
      .substring(startIndex, endIndex)
      .replace(/^\.\/[^\n]+\n/, "") // remove the line with file path
      .trim();

    if (fileContent) {
      contextItems.push({
        name: `Search results in ${filepath}`,
        description: `Grep search results from ${filepath}`,
        content: fileContent,
        uri: { type: "file", value: filepath },
      });
    }
  }

  return contextItems;
}

export const grepSearchImpl: ToolImpl = async (args, extras) => {
  const pattern = getStringArg(args, "pattern");
  // Parse optional parameters but don't use them (as requested)
  const path = getOptionalStringArg(args, "path");
  const include = getOptionalStringArg(args, "include");

  if (!pattern) {
    return [
      {
        name: "Grep Search failed",
        description: "Search failed: Invalid pattern parameter",
        content: "Please provide a valid search pattern string",
        type: "error",
      },
    ];
  }
  const results = await extras.ide?.getSearchResults(args.pattern);
  if (results) {
    const { formatted: formattedResults } = formatGrepSearchResults(results);
    const resultCount = formattedResults?.split("\n")?.length ?? 0;
    return [
      {
        name: `Search Results of ${args.pattern}`,
        description: `Found ${resultCount} matching result${resultCount === 1 ? "" : "s"}`,
        content: formattedResults,
        type: "success",
      },
    ];
  } else {
    return [
      {
        name: `Search Results of ${args.pattern}`,
        description: "No matches found",
        content: `No content found matching the pattern "${args.pattern}"`,
        type: "success",
      },
    ];
  }
};
