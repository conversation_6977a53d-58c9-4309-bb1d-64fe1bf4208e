import { ToolImpl } from ".";
import { getOptionalStringArg, getStringArg } from "../parseArgs";

const MAX_AGENT_GLOB_RESULTS = 100;

export const fileGlobSearchImpl: ToolImpl = async (args, extras) => {
  const pattern = getStringArg(args, "pattern", true);
  // Parse optional path parameter but don't use it (as requested)
  const path = getOptionalStringArg(args, "path");

  if (!pattern) {
    return [
      {
        name: "File Search Failed",
        description: "Invalid search pattern provided",
        content: "Please provide a valid glob pattern to search for files",
        type: "error",
      },
    ];
  }
  const results = await extras.ide.getFileResults(
    pattern,
    MAX_AGENT_GLOB_RESULTS,
  );
  const resultCount = results?.length || 0;
  if (resultCount === 0) {
    return [
      {
        name: `No Files Found: "${pattern}"`,
        description: "No files match the specified pattern",
        content: "",
        type: "success",
      },
    ];
  } else {
    return [
      {
        name: `Files Found: "${pattern}"`,
        description: `Found ${resultCount} file${resultCount === 1 ? "" : "s"} matching the pattern`,
        content: results?.join("\n"),
        type: "success",
      },
    ];
  }
  /*const contextItems: ContextItem[] = [
    {
      name: "File results",
      description: "Results from file glob search",
      content: results.join("\n"),
    },
  ];

  // In case of truncation, add a warning
  if (results.length === MAX_AGENT_GLOB_RESULTS) {
    contextItems.push({
      name: "Truncation warning",
      description: "Inform the model that results were truncated",
      content: `Warning: the results above were truncated to the first ${MAX_AGENT_GLOB_RESULTS} files. If the results are not satisfactory, refine your search pattern`,
    });
  }

  return contextItems;*/
};
