import { stat } from "fs/promises";

const TRUNCATED_MESSAGE =
  "To save on context only part of this file has been shown to you. You should retry this tool after you have searched inside the file with `grep -n` in order to find the line numbers of what you are looking for.";
const MAX_RESPONSE_LEN = 16000;
export const SNIPPET_LINES = 4;

export function maybeTruncate(
  content: string,
  truncateAfter: number = MAX_RESPONSE_LEN,
): string {
  return content.length <= truncateAfter
    ? content
    : content.substring(0, truncateAfter) + "\n\n" + TRUNCATED_MESSAGE;
}

export function makeOutput(
  fileContent: string,
  fileDescriptor: string,
  initLine: number = 1,
): { content: string; description: string } {
  const truncatedContent = maybeTruncate(fileContent);
  const lines = truncatedContent.split("\n");
  const totalLines = lines.length;
  const endLine = initLine + totalLines - 1 - 3;
  const fileLines = fileContent.split("\n").length;

  // 返回纯文本内容和包含行号信息的描述
  return {
    content: truncatedContent,
    description:
      initLine === 1 && totalLines === fileLines
        ? `Contents of ${fileDescriptor}`
        : `Lines ${initLine}-${endLine} of ${fileDescriptor}`,
  };
}

export async function isDirectory(path: string): Promise<boolean> {
  try {
    const stats = await stat(path);
    return stats.isDirectory();
  } catch {
    return false;
  }
}
